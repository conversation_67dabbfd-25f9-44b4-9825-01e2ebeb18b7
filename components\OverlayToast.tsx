import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';

const OverlayToast = ({ text1, text2, ...rest }) => {
  return (
    <View style={styles.overlayContainer}>
      <View style={[styles.toastContent, text1=== 'Error' ? {borderLeftColor: 'red'} : {borderLeftColor: 'green'}]}>
        <Text style={styles.title}>{text1}</Text>
        {text2 ? <Text style={styles.subtitle}>{text2}</Text> : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
    backgroundColor: 'rgba(0,0,0,0.5)', // dim background
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  toastContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    elevation: 5,
    borderLeftWidth: 5,
    
    maxWidth: '80%',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 10,
    textAlign: 'center',
  },
});

export default OverlayToast;
