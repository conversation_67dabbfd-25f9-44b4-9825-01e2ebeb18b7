import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Dimensions,
  ActivityIndicator,
  FlatList,
} from 'react-native';

import { TextInput } from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import {
  Leaf,
  TreeDeciduous,
  Rabbit,
  Tractor,
  MapPin,
  Search,
  Filter,
  Plus,
  ChevronRight,
  ChevronLeft,
  TreePalm
} from 'lucide-react-native';
import { Field, Garden, Animal, Equipment } from '@/types';
import MapView, { Marker, Polygon, Callout, Region } from 'react-native-maps';
import * as Location from 'expo-location';
import { useTranslation } from '@/i18n/useTranslation';
import Input from '@/components/Input';
const { width } = Dimensions.get('window');

export default function MapScreen() {
  const {
    currentFarm,
    fields,
    plants,
    gardens,
    animals,
    equipment,
    fetchFields,
    fetchPlants,
    fetchGardens,
    fetchAnimals,
    fetchEquipment,
    isLoading
  } = useFarmStore();
  const { user } = useAuthStore();
  const mapRef = useRef<MapView | null>(null);

  const [activeTab, setActiveTab] = useState<'fields' | 'gardens' | 'animals' | 'equipment' | 'plants'>('fields');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredItems, setFilteredItems] = useState<any[]>([]);
  const { t, isRTL } = useTranslation()
  useEffect(() => {
    if (currentFarm?.id) {
      fetchFields(currentFarm.id);
      fetchGardens(currentFarm.id);
      fetchPlants(currentFarm.id);
      fetchAnimals(currentFarm.id);
      fetchEquipment(currentFarm.id);
    }
  }, [currentFarm]);
  useEffect(() => {
    // Filter items based on active tab and search query
    let items: any[] = [];
    // debugger
    // console.log('equipment', equipment); 

    switch (activeTab) {
      case 'fields':
        items = fields.filter(field => field && field.location); // Only include fields with location data
        break;
      case 'gardens':
        items = gardens.filter(garden => garden && garden.location); // Only include gardens with location data
        break;
      case 'animals':
        items = animals.filter(animal => animal); // Only include animals with location data
        break;
      case 'equipment':
        items = equipment.filter(equip => equip);//&& equip.location // Only include equipment with location data
        break;
      case 'plants':
        items = plants.filter(plant => plant); // Only include plants with location data
        break;
    }

    if (searchQuery) {
      items = items.filter(item =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (activeTab == "gardens" || activeTab == "fields") { //activeTab == "plants" 
      // Validate location data for each item
      items = items.filter(item => {
        if (!item.location) return false;

        const lat = Number(item.location.latitude);
        const lng = Number(item.location.longitude);

        return !isNaN(lat) && !isNaN(lng);
      });
    }
    // console.log(`Filtered ${items.length} ${activeTab} with valid location data`);
    setFilteredItems(items);
  }, [activeTab, searchQuery, fields, plants, gardens, animals, equipment]);
  const getItemImage = (item: any) => {
    if (item.image) return item.image;

    // Default images based on type
    switch (activeTab) {
      case 'fields':
        return 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8ZmllbGR8ZW58MHx8MHx8&w=1000&q=80';
      case 'gardens':
        return 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8Z2FyZGVufGVufDB8fDB8fA%3D%3D&w=1000&q=80';
      case 'animals':
        return 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NHx8Y293fGVufDB8fDB8fA%3D%3D&w=1000&q=80';
      case 'equipment':
        return 'https://images.unsplash.com/photo-1605000797499-95a51c5269ae?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8dHJhY3RvcnxlbnwwfHwwfHw%3D&w=1000&q=80';
      default:
        return '';
    }
  };
  const regionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleRegionChangeComplete = (mapRegion: Region) => {
    if (regionTimeoutRef.current) {
      clearTimeout(regionTimeoutRef.current);
    }
    regionTimeoutRef.current = setTimeout(() => {
      // setMapRegion(region);

      const isSameRegion =
        mapRegion.latitude === region.latitude &&
        mapRegion.longitude === region.longitude &&
        mapRegion.latitudeDelta === region.latitudeDelta &&
        mapRegion.longitudeDelta === region.longitudeDelta;

      if (!isSameRegion)
        setRegion({
          latitude: Number(mapRegion.latitude),
          longitude: Number(mapRegion.longitude),
          latitudeDelta: Number(mapRegion.latitudeDelta),
          longitudeDelta: Number(mapRegion.longitudeDelta),
        });
    }, 1000); // adjust as needed
  };

  // const getItemSubtitle = (item: any) => {
  //   switch (activeTab) {
  //     case 'fields':
  //       return `${item.size} ${item.sizeUnit} • ${item.type}`;
  //     case 'gardens':
  //       return `${item.size} ${item.sizeUnit} • ${item.type}`;
  //     case 'animals':
  //       return `${item.species}${item.breed ? ` • ${item.breed}` : ''}`;
  //     case 'equipment':
  //       return `${item.type}${item.model ? ` • ${item.model}` : ''}`;
  //     case 'plants':
  //       return `${item?.variety} - ${item?.health}`;
  //     default:
  //       return '';
  //   }
  // };
  const getItemSubtitle = (item: any): string => {
    if (!item) return '';

    switch (activeTab) {
      case 'fields':
        {
          // console.log(item.type)
          const size = item.size?.toFixed(2) ?? '-';
          const sizeUnitKey = `units.${item.sizeUnit?.toLowerCase()}`; // e.g., units.acres
          const typeKey = `fieldTypes.${item.type}`;    // e.g., fieldTypes.crop
          const translatedSizeUnit = t(`common.areaUnit.${item?.sizeUnit}`) //t(sizeUnitKey, item.sizeUnit);    // fallback to raw if missing
          const translatedType = t(`farm.fieldTypeOptions.${item?.type}`);
          return `${size} ${translatedSizeUnit} • ${translatedType}`;
        }
      case 'gardens':
        {
          // console.log(item.type)
          const size = item.size?.toFixed(2) ?? '-';
          const sizeUnitKey = `units.${item.sizeUnit?.toLowerCase()}`; // e.g., units.acres
          const typeKey = `fieldTypes.${item.type}`;    // e.g., fieldTypes.crop
          const translatedSizeUnit = t(`common.areaUnit.${item?.sizeUnit}`) //t(sizeUnitKey, item.sizeUnit);    // fallback to raw if missing
          const translatedType = t(`entity.garden.${item?.gardenType}`);
          return `${size} ${translatedSizeUnit} • ${translatedType}`;
        }
      case 'animals': {
        // console.log({item})
        const species = t(`form.${item?.species}`) //t(`entity.animal.species_${item.species?.toLowerCase()}`, item.species);
        const breed = item.breed ? `• ${t('breeds.' + item.breed + '')}` : '';
        return `${species} ${breed}`;
      }

      case 'equipment': {
        //    {item.type} {item.model ? `• ${item.model}` : ''}
        //    {t(`entity.equipment.equipmentTypes.${item.type?.toLowerCase()}`)}
        const type = `${t('entity.equipment.equipmentTypes.' + item.type?.toLowerCase() + '')}`
        //t(`equipmentTypes.${item.type?.toLowerCase()}`, item.type);
        const model = item.model ? `• ${item.model}` : '';
        return `${type} ${model}`;
      }

      case 'plants': {
        const variety = item.variety ?? t('common.variety');
        const health = t(`entity.plant.status_${item.health}`, item.health);
        return `${variety} - ${health}`;
      }

      default:
        return '';
    }
  };

  const getItemStatus = (item: any) => {
    // console.log({ activeTab },{item})
    switch (activeTab) {
      case 'fields':
      case 'gardens':
        return t(`entity.field.status${item.status}`) || 'active';
        break;

      case 'animals':
        return t(`entity.animal.${item.status}`) || 'active';
        break;

      case 'equipment':
        return t(`entity.equipment.${item.status}`, item.status) || 'active';
        break;


      case 'plants':
        return t(`entity.plant.${item.status}`) || 'active';
        break;

      default:
        return '';
    }


  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'healthy':
      case 'operational':
        return colors.success;
      case 'fallow':
      case 'nursing':
      case 'maintenance':
        return colors.warning;
      case 'inactive':
      case 'sick':
      case 'repair':
        return colors.danger;
      case 'planning':
      case 'pregnant':
        return colors.info;
      case 'quarantined':
      case 'retired':
        return colors.secondary;
      default:
        return colors.gray[500];
    }
  };

  const getTabIcon = (tab: string, isActive: boolean) => {
    const color = isActive ? colors.primary : colors.gray[500];

    switch (tab) {
      case 'fields':
        return <Leaf size={20} color={color} />;
      case 'gardens':
        return <TreeDeciduous size={20} color={color} />;
      case 'animals':
        return <Rabbit size={20} color={color} />;
      case 'equipment':
        return <Tractor size={20} color={color} />;
      case 'plants':
        return <TreePalm size={20} color={color} />;
      default:
        return null;
    }
  };

  const handleItemPress = (item: any) => {
    switch (activeTab) {
      case 'fields':
        router.push(`/field/${item.id}`);
        break;
      case 'gardens':
        router.push(`/garden/${item.id}`);
        break;
      case 'animals':
        router.push(`/animal/${item.id}`);
        // router.push(`/animal/[id]?id=${item.id}$farmid=${}`)
        break;
      case 'equipment':
        router.push(`/equipment/${item.id}`);
        break;
      case 'plants':
        router.push(`/plant/${item.id}`);
        break;
    }
  };

  const handleAddPress = () => {
    switch (activeTab) {
      case 'fields':
        router.push('/field/create');
        break;
      case 'gardens':
        router.push('/garden/create');
        break;
      case 'animals':
        router.push('/animal/create');
        break;
      case 'equipment':
        router.push('/equipment/create');
        break;
    }
  };

  const handleSelectField = (field) => {
    mapRef.current?.animateToRegion({
      latitude: field?.location?.latitude,
      longitude: field?.location?.longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    }, 500); // 500ms animation
  };

  useEffect(() => {
    if (mapRef.current && fields.length > 0) {
      const coordinates = fields.map(f => ({
        latitude: f?.location?.latitude,
        longitude: f?.location?.longitude,
      }));

      mapRef.current.fitToCoordinates(coordinates, {
        edgePadding: { top: 100, right: 100, bottom: 100, left: 100 },
        animated: true,
      });
    }
  }, [fields]);

  const renderItem = ({ item }: { item: Field | Garden | Animal | Equipment }) => (
    // <View  style={[styles.listItem1, isRTL && { flexDirection: 'row-reverse' }]}>
    <View
      style={[styles.listItem, isRTL && { flexDirection: 'row-reverse' }]}

    >
      <TouchableOpacity
      style={{flexDirection:'row',width:'94%'}}
        onPress={() => {
          handleItemPress(item)
          //  handleSelectField(item)
        }}>
        <Image
          source={{ uri: getItemImage(item) }}
          style={styles.listItemImage}
        />
        <View style={styles.listItemContent}>
          <Text style={[styles.listItemTitle, isRTL && { textAlign: 'right', marginRight: 8 }]}>{item.name}</Text>
          <Text style={[styles.listItemSubtitle, isRTL && { textAlign: 'right', marginRight: 8 }]}>{getItemSubtitle(item)}</Text>
          <View style={[styles.listItemStatus, isRTL && { flexDirection: 'row-reverse' }]}>
            <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(getItemStatus(item)) }]} />
            <Text style={[styles.statusText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{getItemStatus(item)}</Text>
          </View>
        </View>
      </TouchableOpacity>

      <TouchableOpacity
       onPress={() => {
          // handleItemPress(item)
           handleSelectField(item)
        }} >
        <MapPin size={24} color={colors.primary} />
      </TouchableOpacity>
      {/* {isRTL ? <ChevronLeft size={20} color={colors.gray[400]} /> : <ChevronRight size={20} color={colors.gray[400]} />} */}

    </View>

    // </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      {getTabIcon(activeTab, true)}
      <Text style={styles.emptyStateText}>{t(`entity.farm.no${activeTab.slice(0, 1).toUpperCase() + activeTab.slice(1)}Added`)}</Text>
      <TouchableOpacity
        style={styles.emptyStateButton}
        onPress={handleAddPress}
      >
        <Text style={styles.emptyStateButtonText}>{t(`entity.farm.add${activeTab.slice(0, 1).toUpperCase() + activeTab.slice(1)}`)}</Text>
      </TouchableOpacity>
    </View>
  );
  // Fix the region state initialization to ensure numeric values
  const [region, setRegion] = useState({
    latitude: 30.3753, // Default to center of Pakistan
    longitude: 69.3451,
    latitudeDelta: 0.009,
    longitudeDelta: 0.009,
  });
  // Fix the userLocation state to ensure it's properly typed
  const [userLocation, setUserLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);

  // Update the useEffect for location to ensure proper type conversion
  useEffect(() => {
    (async () => {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        return;
      }

      try {
        const location = await Location.getCurrentPositionAsync({});
        // Ensure we're using number values
        const latitude = Number(location.coords.latitude);
        const longitude = Number(location.coords.longitude);

        setUserLocation({
          latitude,
          longitude
        });

        setRegion({
          latitude,
          longitude,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        });
      } catch (error) {
        console.error('Error getting location:', error);
      }
    })();
  }, []);
  const returnCount = () => {
    switch (activeTab) {
      case 'fields':
        return fields.length;
      case 'animals':
        return animals.length;
      case 'equipment':
        return equipment.length;
      case 'plants':
        return plants.length;
      case 'gardens':
        return gardens.length;
      default:
        return 0
    }
  }
  const renderFieldMarker = (item: Field) => {
    // console.log('Rendering field:', item); // Add this for debugging

    if (!item?.location) {
      console.log('No location data for field:', item.id);
      return null;
    }

    // Ensure we have valid coordinates
    const latitude = Number(item.location.latitude);
    const longitude = Number(item.location.longitude);

    if (isNaN(latitude) || isNaN(longitude)) {
      console.log('Invalid coordinates for field:', item.id);
      return null;
    }
    // console.log({ item })
    return (
      <React.Fragment key={item.id}>
        {item.location.boundaries && item.location.boundaries.length >= 3 && (
          <Polygon
            coordinates={item.location.boundaries.map(coord => ({
              latitude: Number(coord.latitude),
              longitude: Number(coord.longitude)
            }))}
            strokeColor={colors.primary}
            fillColor={`${colors.primary}50`}
            strokeWidth={2}
          />
        )}

        <Marker
          coordinate={{
            latitude: Number(item.location.boundaries?.coordinates?.[0]?.latitude || item.location.latitude),
            longitude: Number(item.location.boundaries?.coordinates?.[0]?.longitude || item.location.longitude),
          }}
        >
          <View style={[styles.markerContainer, { backgroundColor: colors.primary }]}>
            <Leaf size={20} color={colors.white} />
          </View>

          <Callout style={styles.callout}>
            <View style={styles.calloutContent}>
              <Text style={styles.calloutTitle}>{item.name}</Text>
              <Text>{item.type || 'No type'}</Text>
              <Text>{item.size} {item.sizeUnit}</Text>
            </View>
          </Callout>
        </Marker>
      </React.Fragment>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>


        <View style={[styles.searchContainer, isRTL && { flexDirection: 'row-reverse' }]}>
          <Search size={20} color={colors.gray[500]} style={styles.searchIcon} />
          <Input
            style={[styles.searchInput, isRTL && { textAlign: 'right', marginRight: 8 }]}
            placeholder={t(`search_${activeTab}...`)}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          <TouchableOpacity style={styles.filterButton}>
            <Filter size={20} color={colors.gray[500]} />
          </TouchableOpacity>
        </View>
      </View>
      <View style={[styles.tabsContainer, isRTL && {}]}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={[
            styles.tabs,
            {
              flexDirection: isRTL ? 'row-reverse' : 'row',
              // justifyContent: 'space-between',
              justifyContent: 'flex-start',
              alignItems: 'center',
            }
          ]}

        // inverted={isRTL}
        >
          {[
            { key: 'fields', label: t('entity.farm.Fields') },
            { key: 'plants', label: t('entity.farm.Plants') },
            { key: 'gardens', label: t('entity.farm.Gardens') },
            { key: 'animals', label: t('entity.farm.Animals') },
            { key: 'equipment', label: t('entity.farm.Equipment') }
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[styles.tab, activeTab === tab.key && styles.activeTab]}
              onPress={() => setActiveTab(tab.key)}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === tab.key && styles.activeTabText,
                  { textAlign: isRTL ? 'right' : 'left' }
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* <View style={styles.tabsContainer}>
        <ScrollView
          horizontal
          // style={isRTL ? { flexDirection: 'row-reverse' } : {flexDirection: 'row' }}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={[styles.tabs, isRTL && { flexDirection: 'row-reverse' }]}
        >
          <TouchableOpacity
            style={[styles.tab, activeTab === 'fields' && styles.activeTab]}
            onPress={() => setActiveTab('fields')}
          >
            <Text style={[styles.tabText, activeTab === 'fields' && styles.activeTabText, isRTL && { textAlign: 'right' }]}>{t('entity.farm.Fields')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'gardens' && styles.activeTab]}
            onPress={() => setActiveTab('gardens')}
          >
            <Text style={[styles.tabText, activeTab === 'gardens' && styles.activeTabText, isRTL && { textAlign: 'right' }]}>{t('entity.farm.Gardens')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'animals' && styles.activeTab]}
            onPress={() => setActiveTab('animals')}
          >
            <Text style={[styles.tabText, activeTab === 'animals' && styles.activeTabText, isRTL && { textAlign: 'right' }]}>{t('entity.farm.Animals')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'equipment' && styles.activeTab, isRTL && { textAlign: 'right' }]}
            onPress={() => setActiveTab('equipment')}
          >
            <Text style={[styles.tabText, activeTab === 'equipment' && styles.activeTabText]}>{t('entity.farm.Equipment')}</Text>
          </TouchableOpacity>

        </ScrollView>
      </View> */}

      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          style={styles.mapImage}
          region={region}
          mapType="satellite"
          // onRegionChangeComplete={(newRegion) => {
          //   // Ensure all values are numbers
          //   setRegion({
          //     latitude: Number(newRegion.latitude),
          //     longitude: Number(newRegion.longitude),
          //     latitudeDelta: Number(newRegion.latitudeDelta),
          //     longitudeDelta: Number(newRegion.longitudeDelta),
          //   });
          // }}
          onRegionChangeComplete={handleRegionChangeComplete}
        >
          {/* Render field markers with proper type conversion */}
          {activeTab === 'fields' && filteredItems.map((item) => renderFieldMarker(item as Field))}

          {/* Render other markers with proper type conversion */}
          {filteredItems.map((item) => {
            if (activeTab === 'fields') {
              return null; // Already handled above
            }

            // Skip items without location data
            if (!item.location || !item.location.latitude || !item.location.longitude) {
              return null;
            }

            let markerColor;
            let MarkerIcon = Leaf; // Set a default icon

            switch (activeTab) {
              case 'gardens':
                markerColor = colors.success;
                MarkerIcon = TreeDeciduous;
                break;
              case 'plants':
                markerColor = colors.success;
                MarkerIcon = Leaf;
                break;
              case 'animals':
                markerColor = colors.warning;
                MarkerIcon = Rabbit;
                break;
              case 'equipment':
                markerColor = colors.info;
                MarkerIcon = Tractor;
                break;
              default:
                markerColor = colors.gray[500];
            }

            return (
              <Marker
                key={item.id}
                coordinate={{
                  latitude: Number(item.location.latitude),
                  longitude: Number(item.location.longitude),
                }}
                onPress={() => handleItemPress(item)}
              >
                <View style={[styles.markerContainer, { backgroundColor: markerColor }]}>
                  <MarkerIcon size={20} color={colors.white} />
                </View>
                <Callout tooltip>
                  <View style={styles.calloutContainer}>
                    <View style={styles.calloutBubble}>
                      <Image
                        source={{ uri: getItemImage(item) }}
                        style={styles.calloutImage}
                      />
                      <View style={styles.calloutContent}>
                        <Text style={styles.calloutTitle}>{item.name}</Text>
                        <Text style={styles.calloutSubtitle}>{getItemSubtitle(item)}</Text>
                        <View style={styles.calloutStatus}>
                          <View
                            style={[
                              styles.statusIndicator,
                              { backgroundColor: getStatusColor(getItemStatus(item)) }
                            ]}
                          />
                          <Text style={styles.statusText}>{getItemStatus(item)}</Text>
                        </View>
                      </View>
                    </View>
                    <View style={styles.calloutArrow} />
                  </View>
                </Callout>
              </Marker>
            );
          })}

          {/* Render user location marker with proper type conversion */}
          {userLocation && (
            <Marker
              coordinate={{
                latitude: Number(userLocation.latitude),
                longitude: Number(userLocation.longitude),
              }}
            >
              <View style={styles.userMarkerContainer}>
                <MapPin size={24} color={colors.secondary} />
              </View>
            </Marker>
          )}
        </MapView>
        {/* <TouchableOpacity
          style={styles.locationButton}
          onPress={() => userLocation && setRegion({
            latitude: userLocation.latitude,
            longitude: userLocation.longitude,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          })}
        >
          <MapPin size={24} color={colors.primary} />
        </TouchableOpacity> */}

        {/* Update the location button to ensure proper type conversion */}
        <TouchableOpacity
          style={styles.locationButton}
          onPress={() => userLocation && setRegion({
            latitude: Number(userLocation.latitude),
            longitude: Number(userLocation.longitude),
            latitudeDelta: 0.00922,
            longitudeDelta: 0.00921,
          })}
        >
          <MapPin size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.listContainer}>
        {/* <View style={[styles.listHeader,isRTL &&{flexDirection:'row-reverse'}]}>
          <Text style={styles.listTitle}>{activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={handleAddPress}
          >
            <Plus size={20} color={colors.white} />
          </TouchableOpacity>
        </View> */}
        <View style={[styles.listHeader, isRTL && { flexDirection: 'row-reverse' }]}>
          <Text style={[styles.listTitle,]}>{t(`entity.farm.${activeTab}`)} ({returnCount()})</Text>
          <TouchableOpacity
            style={[styles.addButton, isRTL && { flexDirection: 'row-reverse' }]}
            onPress={() => {
              switch (activeTab) {
                case 'fields':
                  router.push('/field/create')
                  return;
                case 'plants':
                  router.push('/plant/create')
                  return;
                case 'animals':
                  router.push('/animal/create')
                  return;
                case 'equipment':
                  router.push('/equipment/create')
                  return;
                case 'gardens':
                  router.push('/garden/create')
                  return;
              }
            }}
          >
            <Plus size={20} color={colors.white} />
            <Text style={[styles.addButtonText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t(`entity.farm.add${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}`)}</Text>
          </TouchableOpacity>
        </View>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : (
          <FlatList
            data={filteredItems}
            renderItem={renderItem}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={renderEmptyState}
          />
        )}
      </View>
    </View>
  );
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
    marginLeft: 8,
  },
  callout: {
    minWidth: 200,
    padding: 10,
    backgroundColor: colors.white,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  header: {
    backgroundColor: colors.white,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: colors.gray[800],
  },
  filterButton: {
    padding: 8,
  },
  tabsContainer: {
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  tabs: {
    paddingHorizontal: 16,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 16,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.gray[600],
    fontWeight: '500',
    marginLeft: 8,
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  mapContainer: {
    height: 200,
    position: 'relative',
  },
  mapImage: {
    width: '100%',
    height: '100%',
  },
  mapOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  map: {
    width: '100%',
    height: '100%',
  },
  listContainer: {
    flex: 1,
    padding: 16,
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  addButton: {
    // backgroundColor: colors.primary,
    // width: 40,
    // height: 40,
    // borderRadius: 20,
    // justifyContent: 'center',
    // alignItems: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    paddingBottom: 16,
  },
  listItem1: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    // borderRadius: 12,
    // padding: 12,
    // marginBottom: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    alignItems: 'center',
  },
  listItem: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 12,
    width:'98%',
    marginBottom: 6,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    alignItems: 'center',
  },
  listItemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  listItemContent: {
    flex: 1,
  },
  listItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  listItemSubtitle: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  listItemStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  statusText: {
    fontSize: 12,
    color: colors.gray[600],
    textTransform: 'capitalize',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyStateText: {
    fontSize: 16,
    color: colors.gray[600],
    marginTop: 12,
    marginBottom: 24,
  },
  emptyStateButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  calloutContainer: {
    padding: 8,
    minWidth: 150,
  },
  calloutTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  calloutSubtitle: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 4,
  },
  calloutStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationButton: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    backgroundColor: colors.white,
    padding: 12,
    borderRadius: 24,
    shadowColor: colors.gray[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  markerContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 3,
  },
  userMarkerContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 3,
  },
  calloutBubble: {
    backgroundColor: colors.white,
    borderRadius: 6,
    padding: 10,
    width: 150,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  calloutImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  calloutContent: {
    flex: 1,
  },
  calloutArrow: {
    position: 'absolute',
    top: -10,
    left: 70,
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderTopWidth: 10,
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderTopColor: colors.white,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
  },
  markerContainer: {
    padding: 5,
    backgroundColor: 'white',
    borderRadius: 50,
    borderWidth: 2,
    borderColor: colors.primary,
  },
  calloutContent: {
    minWidth: 200,
    padding: 10,
  },
  calloutInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 2,
  },
  infoLabel: {
    color: colors.gray[600],
    fontSize: 12,
  },
  infoValue: {
    color: colors.gray[900],
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 5,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5,
  },
  statusText: {
    fontSize: 12,
    color: colors.gray[600],
  },
});



