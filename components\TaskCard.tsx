import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, I18nManager } from 'react-native';
import { useTranslation } from '@/i18n/useTranslation';
import { CheckCircle2, AlertCircle } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { Task } from '@/types';

interface TaskCardProps {
  task: Task;
  onPress?: (task: Task) => void;
}

// const placeholderImage = require('@/assets/images/placeholder.png');

export default function TaskCard({ task, onPress }: TaskCardProps) {
  const { t,isRTL } = useTranslation();
  // const isRTL = i18n.dir() === 'rtl';

  const isCompleted = task.status === 'completed';
  const statusColor = isCompleted ? colors.success[600] : colors.info[600];
  const statusIcon = isCompleted ? (
    <CheckCircle2 size={14} color={statusColor} />
  ) : (
    <AlertCircle size={14} color={statusColor} />
  );

  return (
    <TouchableOpacity
      onPress={() => onPress?.(task)}
      activeOpacity={0.9}
      style={[styles.card, isRTL && styles.cardRtl]}
    >
      <Image
        source={task?.imageUrl ? { uri: task.imageUrl[0]?.url } : null}
        style={[styles.image, isRTL ? styles.imageRtl : styles.imageLtr]}
        resizeMode="cover"
      />

      <View style={[styles.content, isRTL && styles.contentRtl]}>
        <Text style={[styles.title, isRTL && styles.textAlignRight]} numberOfLines={1}>
          {task.title}
        </Text>

        <Text style={[styles.description, isRTL && styles.textAlignRight]} numberOfLines={2}>
          {task.description}
        </Text>

        <View style={styles.metaRow}>
          {statusIcon}
          <Text style={[styles.status, { color: statusColor }]}>
            {t(`task.status.${task.status}`)}
          </Text>
          <Text style={styles.dot}>•</Text>
          <Text style={styles.date}>
            {new Date(task.dueDate).toLocaleDateString()}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 5,
    height: 100,
  },
  cardRtl: {
    flexDirection: 'row-reverse',
  },
  image: {
    width: 100,
    height: '100%',
    backgroundColor: colors.gray[200],
  },
  imageLtr: {
    borderTopLeftRadius: 16,
    borderBottomLeftRadius: 16,
  },
  imageRtl: {
    borderTopRightRadius: 16,
    borderBottomRightRadius: 16,
  },
  content: {
    flex: 1,
    padding: 12,
    justifyContent: 'center',
  },
  contentRtl: {
    alignItems: 'flex-end',
  },
  title: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.gray[900],
    marginBottom: 2,
  },
  description: {
    fontSize: 13,
    color: colors.gray[600],
    marginBottom: 6,
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  status: {
    fontSize: 12,
    fontWeight: '600',
  },
  dot: {
    fontSize: 10,
    color: colors.gray[500],
    marginHorizontal: 4,
  },
  date: {
    fontSize: 12,
    color: colors.gray[500],
  },
  textAlignRight: {
    textAlign: 'right',
  },
});
