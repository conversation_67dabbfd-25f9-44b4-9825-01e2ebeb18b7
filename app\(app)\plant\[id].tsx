import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert as RNAlert,
  Modal,
  ActivityIndicator,
  Alert,
  FlatList
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import {
  MoreVertical,
  Edit,
  Trash2,
  ChevronRight,
  Plus,
  Calendar,
  Thermometer,
  Pill,
  Camera,
  Sprout,
  CheckCircle,
  ClipboardList,
  QrCode,
  Check,
  Square,
  CheckSquare,
  X,
  MapPin,
  DollarSign,
  Archive,
  Droplets,
  Sun,
  Bug,
  Clock,
  Scissors,
  AlertCircle,
  Heart
} from 'lucide-react-native';
import { useState, useEffect, useRef } from 'react';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { useTranslation } from '@/i18n/useTranslation';
import { analyzePlantHealth } from '@/utils/openai-vision';
import Button from '@/components/Button';
import { Plant, ChecklistItem, Task } from '@/types';
import ChecklistForm from '@/components/ChecklistForm';
import ImagePicker from '@/components/ImagePicker';
import QRCodeDisplay from '@/components/QRCodeDisplay';
import { generateUniversalLink } from '@/utils/qrcode';
import InactiveStatusModal from '@/components/InactiveStatusModal';
import Toast from 'react-native-toast-message';
import FinanceHandlerButton from '@/components/FinanceHandlerButton';
import FinanceBottomSheet from '@/components/FinanceBottomSheet';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useRouter } from 'expo-router';
import { collection, addDoc, query, where, getDocs, orderBy, serverTimestamp } from 'firebase/firestore';
import { firestore } from '@/firebase/config';
import React from 'react';
import EntityChecklistManager from '@/components/EntityChecklistManager';
import { EntityGallery } from '@/components/EntityGallery';
export default function PlantDetailScreen() {
  const { id } = useLocalSearchParams();
  // console.log({ id })
  const { user } = useAuthStore();
  const {
    getPlant,
    updatePlant,
    deletePlant,
    addTask,
    tasks,
    fetchTasks,
    currentFarm,
    isLoading,
    markPlantInactive,
    saveChecklist,
    getTodayChecklist,
    loadEntityGallery
  } = useFarmStore();
  const { t, isRTL } = useTranslation();
  const [plant, setPlant] = useState<Plant | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'tasks' | 'checklists' | 'history' | 'gallery' | 'qrcode'>('overview');
  const [showOptions, setShowOptions] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showChecklistModal, setShowChecklistModal] = useState(false);
  const [showImagePickerModal, setShowImagePickerModal] = useState(false);
  const [showImagePickerModalAI, setShowImagePickerModalAI] = useState(false);
  const [showInactiveModal, setShowInactiveModal] = useState(false);
  const [showFinanceBottomSheet, setShowFinanceBottomSheet] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [healthAnalysis, setHealthAnalysis] = useState<any>(null);
  const router = useRouter();
  const [isInactive, setIsInactive] = useState(false);
  const financeBottomSheetRef = useRef<BottomSheetModal>(null)
  const [todayCheckListData, setTodayCheckListData] = useState(undefined)
  const [checkListSelectedData, setCheckListSelectedData] = useState([] as any)
  const [isLoadingAdd, setIsLoadingAdd] = useState(false)
  //  const isInactive = plant.isInactive === true;
  const [showChecklistSelector, setShowChecklistSelector] = useState(false);
  const [availableChecklists, setAvailableChecklists] = useState([]);
  const [isLoadingChecklists, setIsLoadingChecklists] = useState(false);
  const [selectedChecklistTemplate, setSelectedChecklistTemplate] = useState(null);

  useEffect(() => {
    if (id && currentFarm) {
      const plantId = Array.isArray(id) ? id[0] : id;
      const plantData = getPlant(plantId);
      // loadTodayCheckListData()
      // console.log({ plantData })
      if (plantData) {
        setIsInactive(plantData?.isInactive)
        setPlant(plantData);
      } else {
        Alert.alert('Error', 'Plant not found');
        router.back();
      }

      fetchTasks(currentFarm.id);
    }
  }, [id, currentFarm]);
  useEffect(() => {

    if (checkListSelectedData.length > 0) {
      const today = new Date().toISOString().split("T")[0];

      // where("date", ">=", `${today}T00:00:00`),
      // where("date", "<=", `${today}T23:59:59`)
      const res = checkListSelectedData.find((x: any) => x?.date >= `${today}T00:00:00` && x?.date <= `${today}T23:59:59`)
      // console.log({ res })
      setTodayCheckListData(res)
    }
    else {
      setTodayCheckListData(undefined)
    }
  }, [checkListSelectedData])

  // }, [])
  // const { t } = useTranslation();
  const handleAnalyzePlantHealth = async (uri) => {
    setShowImagePickerModalAI(false)
    // if (!plant?.image) {
    if (!uri) {
      Alert.alert('No Image', 'An image is required to analyze the plant\'s health.');
      return;
    }
    setIsAnalyzing(true);
    try {
      const analysis = await analyzePlantHealth(uri);
      if (analysis) {
        setHealthAnalysis(analysis);
      } else {
        Alert.alert('Error', 'Failed to get analysis from the AI model.');
      }
    } catch (error) {
      console.error('Plant health analysis error:', error);
      Alert.alert('Error', 'An error occurred while analyzing the plant health.');
    } finally {
      setIsAnalyzing(false);
    }
  }
  // Add this function to navigate to the finance screen
  const handleNavigateToFinance = () => {
    router.push({
      pathname: '/finance/[entityId]',
      params: { entityId: plant.id, entityType: 'plant' }
    });
  };

  // Add this function to handle opening the finance bottom sheet
  // function handleOpenFinanceBottomSheet(): void {
  //   financeBottomSheetRef.current?.present();
  //   setShowFinanceBottomSheet(true);
  // }

  // // Add this function to handle closing the finance bottom sheet
  // function handleCloseFinanceBottomSheet(): void {
  //   financeBottomSheetRef.current?.dismiss();
  //   setShowFinanceBottomSheet(false);
  // }
  // const checklistItems = [
  //   {
  //     key: 'moisture',
  //     title: 'Check soil moisture',
  //     description: 'Ensure soil is appropriately moist but not waterlogged',
  //     checked: false,
  //   },
  //   {
  //     key: 'pests',
  //     title: 'Inspect for pests',
  //     description: 'Look for insects, eggs, or damage on leaves and stems',
  //     checked: false,
  //   },
  //   {
  //     key: 'disease',
  //     title: 'Check for disease',
  //     description: 'Look for discoloration, spots, or unusual growth',
  //     checked: false,
  //   },
  //   {
  //     key: 'prune',
  //     title: 'Prune if necessary',
  //     description: 'Remove dead or damaged leaves and branches',
  //     checked: false,
  //   },
  // ];
  // // Checklist template for plants
  // const plantChecklistTemplate: any[] = [
  //   { id: '1', title: 'Check soil moisture', key: 'moisture', description: 'Ensure soil is appropriately moist but not waterlogged', completed: false, required: true },
  //   { id: '2', title: 'Inspect for pests', key: 'pests', description: 'Look for insects, eggs, or damage on leaves and stems', completed: false, required: true },
  //   { id: '3', title: 'Check for disease', key: 'disease', description: 'Look for discoloration, spots, or unusual growth', completed: false, required: true },
  //   { id: '4', title: 'Prune if necessary', key: 'prune', description: 'Remove dead or damaged leaves and branches', completed: false, required: false },
  // ];
  // { id: '5', title: 'Check growth progress',key: 'prune', description: 'Measure height or count new leaves if applicable', completed: false, required: false },

  // Mock history data
  const [history, setHistory] = useState([
    {
      id: '1',
      type: 'checklist_completed',
      date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      userId: '1',
      userName: 'John Doe',
      title: 'Weekly Inspection',
      description: 'Completed weekly health check',
      images: ['https://images.unsplash.com/photo-1591857177580-dc82b9ac4e1e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80'],
    },
    {
      id: '2',
      type: 'status_change',
      date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
      userId: '1',
      userName: 'John Doe',
      title: 'Status Updated',
      description: 'Changed status from seedling to growing',
    },
    {
      id: '3',
      type: 'note_added',
      date: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(),
      userId: '2',
      userName: 'Jane Smith',
      title: 'Observation',
      description: 'Plant is growing well, new leaves appearing',
      images: ['https://images.unsplash.com/photo-1591857177580-dc82b9ac4e1e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80'],
    },
  ]);

  // useEffect(()=>{

  // },[])

  // Mock gallery images
  const [gallery, setGallery] = useState([
    'https://images.unsplash.com/photo-1591857177580-dc82b9ac4e1e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    'https://images.unsplash.com/photo-1591857177-7c6e912bb340?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    'https://images.unsplash.com/photo-1591857177-96ac90cf5510?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
  ]);
  const loadTodayCheckListData = async () => {
    if (!currentFarm?.id || !id) return;

    try {
      // Get today's date range
      const today = new Date().toISOString().split("T")[0];
      const startOfDay = `${today}T00:00:00`;
      const endOfDay = `${today}T23:59:59`;

      // Query the plantsChecklist collection
      const plantsChecklistRef = collection(firestore, 'plantsChecklist');
      const q = query(
        plantsChecklistRef,
        where('plantId', '==', id),
        where('farmId', '==', currentFarm.id),
        where('date', '>=', startOfDay),
        where('date', '<=', endOfDay),
        orderBy('date', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const checklists = [];

      querySnapshot.forEach((doc) => {
        checklists.push({
          id: doc.id,
          ...doc.data()
        });
      });

      // Also fetch historical checklists for this plant
      const historicalQuery = query(
        plantsChecklistRef,
        where('plantId', '==', id),
        where('farmId', '==', currentFarm.id),
        orderBy('date', 'desc')
      );

      const historicalSnapshot = await getDocs(historicalQuery);
      const allChecklists = [];

      historicalSnapshot.forEach((doc) => {
        allChecklists.push({
          id: doc.id,
          ...doc.data()
        });
      });

      setCheckListSelectedData(allChecklists);

      // Find today's checklist
      const todayChecklist = allChecklists.find(
        x => x?.date >= startOfDay && x?.date <= endOfDay
      );

      if (todayChecklist) {
        setTodayCheckListData(todayChecklist);
      } else {
        setTodayCheckListData(undefined);
      }
    } catch (error) {
      console.error('Error loading checklist data:', error);
      Toast.show({
        type: 'error',
        text1: t('common.error'),
        text2: t('entity.plant.checklist.loadError')
      });
    }
  };
  if (!plant || isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  const handleDeletePlant = async () => {
    try {
      await deletePlant(plant.id);
      router.back();
    } catch (error) {
      console.error('Error deleting plant:', error);
      Alert.alert('Error', 'Failed to delete plant');
    }
  };

  const handleSaveChecklist = async (items: ChecklistItem[], images: string[], notes: string) => {
    try {
      setIsLoadingAdd(true)
      // In a real app, you would save the checklist to the database
      // For now, we'll just add it to the history
      const newHistoryEntry = {
        id: Date.now().toString(),
        type: 'checklist_completed',
        date: new Date().toISOString(),
        userId: user?.id || '',
        userName: user?.name || '',
        plantId: plant.id,
        title: items?.title,// 'Plant Inspection',
        description: notes || 'Completed plant health check',
        checklistItems: items,
        images,
      };
      // console.log()
      const res = await saveChecklist(currentFarm?.id, newHistoryEntry)
      if (res) {

        setShowChecklistModal(false);
        Alert.alert('Success', 'Checklist saved successfully');
      } else {
        Alert.alert('Error', 'Failed to save checklist');
      }
      setIsLoadingAdd(false)

      // setHistory([newHistoryEntry, ...history]);
      //


    } catch (error) {
      console.error('Error saving checklist:', error);
      Alert.alert('Error', 'Failed to save checklist');
    }
  };

  const handleAddImage = (uri: string) => {
    setGallery([uri, ...gallery]);
    setShowImagePickerModal(false);
  };

  const handleCreateTask = async () => {
    router.push(`/task/create?id=${plant.id}&entity=plant`)
    // router.push({
    //   pathname: '/task/create',
    //   params: {
    //     // title: `Check ${plant.name}`,
    //     // description: `Perform health check and maintenance for ${plant.name}`,
    //     // status: 'pending',
    //     // priority: 'medium',
    //     // dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    //     // farmId: currentFarm.id,
    //     plantId: plant.id,
    //     entity: 'plant',
    //   },
    // });
    // }
    try {
      if (!currentFarm) {
        Alert.alert('Error', 'No farm selected');
        return;
      }

      const taskData = {
        title: `Check ${plant.name}`,
        description: `Perform health check and maintenance for ${plant.name}`,
        status: 'pending',
        priority: 'medium',
        dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        farmId: currentFarm.id,
        plantId: plant.id,
        entityType: 'plant',
        assignedBy: user?.id,
        checklist: plantChecklistTemplate,
      };

      await addTask(taskData);

      Alert.alert('Success', 'Task created successfully');
    } catch (error) {
      console.error('Error creating task:', error);
      Alert.alert('Error', 'Failed to create task');
    }
  };

  const handleMarkInactive = async (data: {
    reason: string;
    notes?: string;
    image?: string;
  }) => {
    try {
      await markPlantInactive(plant.id, data);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Plant marked as inactive',
      });
    } catch (error) {
      console.error('Error marking plant as inactive:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to mark plant as inactive',
      });
    }
  };

  // Filter tasks related to this plant
  const plantTasks = tasks.filter(task => task.entityId === plant.id);

  const renderOverviewTab = () => {
    // const isInactive = plant.isInactive === true;

    return (
      <View style={styles.tabContent}>
        <View style={styles.infoCard}>
          {/* Item ID display */}
          {plant.itemId && (
            <View style={styles.itemIdContainer}>
              <View style={styles.itemIdHeader}>
                <QrCode size={20} color={colors.primary} />
                <Text style={styles.itemIdTitle}>{t('entity.plant.itemId')}</Text>
              </View>
              <Text style={styles.itemIdValue}>{plant.itemId}</Text>
              <TouchableOpacity
                style={styles.viewQrButton}
                onPress={() => setActiveTab('qrcode')}
              >
                <Text style={styles.viewQrButtonText}>{t('common.viewQrCode')}</Text>
                <ChevronRight size={16} color={colors.primary} />
              </TouchableOpacity>
            </View>
          )}

          <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
            <View style={[styles.infoItem, isRTL && { flexDirection: 'row-reverse' }]}>
              <Text style={[styles.infoLabel, isRTL && { textAlign: 'right' }]}>{t('entity.plant.species')}</Text>
              <Text style={[styles.infoValue, isRTL && { textAlign: 'right' }]}>{plant.species}</Text>
            </View>

            {plant.variety && (
              <View style={[styles.infoItem,]}>
                <Text style={[styles.infoLabel, isRTL && { textAlign: 'right' }]}>{t('entity.plant.variety')}</Text>
                <Text style={[styles.infoValue, isRTL && { textAlign: 'right' }]}>{plant.variety}</Text>
              </View>
            )}
          </View>

          <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
            <View style={styles.infoItem}>
              <Text style={[styles.infoLabel, isRTL && { textAlign: 'right' }]}>{t('entity.plant.plantedDate')}</Text>
              <Text style={[styles.infoValue, isRTL && { textAlign: 'right' }]}>
                {new Date(plant.plantedDate).toLocaleDateString()}
              </Text>
            </View>

            {plant.expectedHarvestDate && (
              <View style={[styles.infoItem, isRTL && { flexDirection: 'row-reverse' }]}>
                <Text style={[styles.infoLabel, isRTL && { textAlign: 'right' }]}>{t('entity.plant.expectedHarvest')}</Text>
                <Text style={[styles.infoValue, isRTL && { textAlign: 'right' }]}>
                  {new Date(plant.expectedHarvestDate).toLocaleDateString()}
                </Text>
              </View>
            )}
          </View>

          <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
            <View style={styles.infoItem}>
              <Text style={[styles.infoLabel, isRTL && { textAlign: 'right' }]}>{t('entity.plant.status')}</Text>
              <View style={[styles.statusContainer, isRTL && { flexDirection: 'row-reverse' }]}>
                <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(plant.status) }]} />
                <Text style={[styles.statusText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{formatStatus(plant.status)}</Text>
              </View>
            </View>

            <View style={styles.infoItem}>
              <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.plant.health')}</Text>
              <View style={[styles.statusContainer, isRTL && { flexDirection: 'row-reverse' }]}>
                <View style={[styles.statusIndicator, { backgroundColor: getHealthColor(plant.health) }]} />
                <Text style={[styles.statusText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{formatHealth(plant.health)}</Text>
              </View>
            </View>
          </View>

          {plant.fieldId && (
            <View style={styles.infoRow}>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>{t('entity.plant.field')}</Text>
                <TouchableOpacity
                  style={styles.locationButton}
                  onPress={() => router.push(`/field/${plant.fieldId}`)}
                >
                  <MapPin size={16} color={colors.primary} />
                  <Text style={styles.locationText}>{t('common.viewField')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {plant.gardenId && (
            <View style={styles.infoRow}>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>{t('entity.plant.garden')}</Text>
                <TouchableOpacity
                  style={styles.locationButton}
                  onPress={() => router.push(`/garden/${plant.gardenId}`)}
                >
                  <MapPin size={16} color={colors.primary} />
                  <Text style={styles.locationText}>{t('common.viewGarden')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {plant.notes && (
            <View style={[styles.notesContainer]}>
              <Text style={[styles.notesLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.plant.notes')}</Text>
              <Text style={[styles.notesText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{plant.notes}</Text>
            </View>
          )}
        </View>
        {plant.isInactive && (
          <View style={styles.inactiveDetailsContainer}>
            <Text style={styles.inactiveDetailsTitle}>{t('entity.plant.inactiveDetails')}</Text>

            <View style={styles.inactiveDetailsRow}>
              <Text style={styles.inactiveDetailsLabel}>{t('entity.plant.reason')}:</Text>
              <Text style={styles.inactiveDetailsValue}>
                {plant.inactiveReason || t('entity.plant.notSpecified')}
              </Text>
            </View>

            {plant.inactiveDate && (
              <View style={styles.inactiveDetailsRow}>
                <Text style={styles.inactiveDetailsLabel}>{t('entity.plant.date')}:</Text>
                <Text style={styles.inactiveDetailsValue}>
                  {new Date(plant.inactiveDate).toLocaleDateString()}
                </Text>
              </View>
            )}

            {plant.inactiveNotes && (
              <View style={styles.inactiveDetailsRow}>
                <Text style={styles.inactiveDetailsLabel}>{t('entity.plant.notes')}:</Text>
                <Text style={styles.inactiveDetailsValue}>{plant.inactiveNotes}</Text>
              </View>
            )}

            {plant.inactiveImage && (
              <View style={styles.inactiveImageContainer}>
                <Text style={styles.inactiveDetailsLabel}>{t('entity.plant.image')}:</Text>
                <Image
                  source={{ uri: plant.inactiveImage }}
                  style={styles.inactiveImage}
                  resizeMode="cover"
                />
              </View>
            )}
          </View>
        )}
        <View style={styles.quickActionsContainer}>
          <Text style={[styles.sectionTitle, isRTL && { textAlign: 'right' }]}>{t('entity.plant.quickActions')}</Text>
          <View style={styles.quickActions}>

            {/* Health Check */}
            <TouchableOpacity
              style={[
                styles.quickActionButton,
                plant.isInactive && styles.disabledActionButton,
                isRTL && { flexDirection: 'row-reverse' }
              ]}
              onPress={() => setShowChecklistModal(true)}
              disabled={plant.isInactive}
            >
              <View style={[
                styles.quickActionIcon,
                { backgroundColor: plant.isInactive ? colors.gray[400] : colors.primary }
              ]}>
                <ClipboardList size={20} color={colors.white} />
              </View>
              <Text style={[
                styles.quickActionText,
                plant.isInactive && styles.disabledActionText,
                isRTL && { textAlign: 'right', marginRight: 8 }
              ]}>
                {t('entity.plant.healthCheck')}
              </Text>
            </TouchableOpacity>

            {/* Create Task */}
            <TouchableOpacity
              style={[
                styles.quickActionButton,
                plant.isInactive && styles.disabledActionButton,
                isRTL && { flexDirection: 'row-reverse' }
              ]}
              onPress={() => router.push(`/task/create?id=${plant.id}&entity=plant`)}
              disabled={plant.isInactive}
            >
              <View style={[
                styles.quickActionIcon,
                { backgroundColor: plant.isInactive ? colors.gray[400] : colors.warning }
              ]}>
                <Calendar size={20} color={colors.white} />
              </View>
              <Text style={[
                styles.quickActionText,
                plant.isInactive && styles.disabledActionText,
                isRTL && { textAlign: 'right', marginRight: 8 }
              ]}>
                {t('entity.plant.createTask')}
              </Text>
            </TouchableOpacity>

            {/* Add Photo */}
            <TouchableOpacity
              style={[
                styles.quickActionButton,
                plant.isInactive && styles.disabledActionButton,
                isRTL && { flexDirection: 'row-reverse' }
              ]}
              onPress={() => setShowImagePickerModal(true)}
              disabled={plant.isInactive}
            >
              <View style={[
                styles.quickActionIcon,
                { backgroundColor: plant.isInactive ? colors.gray[400] : colors.info }
              ]}>
                <Camera size={20} color={colors.white} />
              </View>
              <Text style={[
                styles.quickActionText,
                plant.isInactive && styles.disabledActionText,
                isRTL && { textAlign: 'right', marginRight: 8 }
              ]}>
                {t('entity.plant.addPhoto')}
              </Text>
            </TouchableOpacity>

            {/* Edit Plant */}
            <TouchableOpacity
              style={[
                styles.quickActionButton,
                plant.isInactive && styles.disabledActionButton,
                isRTL && { flexDirection: 'row-reverse' }
              ]}
              disabled={plant.isInactive}
              onPress={() => router.push({
                pathname: '/plant/create',
                params: { editMode: true, id: plant.id }
              })}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: colors.success }]}>
                <Edit size={20} color={colors.white} />
              </View>
              <Text style={[styles.quickActionText, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                {t('entity.plant.editPlant')}
              </Text>
            </TouchableOpacity>

            {/* Manage Finance */}
            <TouchableOpacity
              style={[
                styles.quickActionButton,
                plant.isInactive && styles.disabledActionButton,
                isRTL && { flexDirection: 'row-reverse' }
              ]}
              onPress={() => router.push(`/finance/create?entityId=${plant.id}&entityType=plant`)}
              disabled={plant.isInactive}
            >
              <View style={[
                styles.quickActionIcon,
                { backgroundColor: plant.isInactive ? colors.gray[400] : colors.warning }
              ]}>
                <DollarSign size={20} color={colors.white} />
              </View>
              <Text style={[
                styles.quickActionText,
                plant.isInactive && styles.disabledActionText,
                isRTL && { textAlign: 'right', marginRight: 8 }
              ]}>
                {t('entity.plant.manageFinance')}
              </Text>
            </TouchableOpacity>

            {/* Mark Inactive */}
            {!plant.isInactive && (
              <TouchableOpacity
                style={[styles.quickActionButton, isRTL && { flexDirection: 'row-reverse' }]}
                onPress={() => setShowInactiveModal(true)}
              >
                <View style={[styles.quickActionIcon, { backgroundColor: colors.gray[500] }]}>
                  <Archive size={20} color={colors.white} />
                </View>
                <Text style={[styles.quickActionText, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                  {t('entity.plant.markInactive')}
                </Text>
              </TouchableOpacity>
            )}

          </View>
        </View>
        <View style={styles.careGuideContainer}>
          <Text style={styles.sectionTitle}>Care Guide</Text>

          <View style={styles.careGuideItem}>
            <View style={[styles.careGuideIcon, { backgroundColor: colors.info + '20' }]}>
              <Droplets size={20} color={colors.info} />
            </View>
            <View style={styles.careGuideContent}>
              <Text style={styles.careGuideTitle}>Watering</Text>
              <Text style={styles.careGuideText}>
                {plant.helpGuide?.watering || 'Water regularly, allowing soil to dry slightly between waterings.'}
                {/* {plant.species === 'Tomato'
                  ? 'Keep soil consistently moist but not waterlogged. Water deeply 2-3 times per week.'
                  : 'Water regularly, allowing soil to dry slightly between waterings.'} */}
              </Text>
            </View>
          </View>

          <View style={styles.careGuideItem}>
            <View style={[styles.careGuideIcon, { backgroundColor: colors.warning + '20' }]}>
              <Sun size={20} color={colors.warning} />
            </View>
            <View style={styles.careGuideContent}>
              <Text style={styles.careGuideTitle}>Sunlight</Text>
              <Text style={styles.careGuideText}>
                {plant.helpGuide?.sunlight || 'Provide adequate sunlight based on plant requirements.'}
                {/* {plant.species === 'Tomato'
                  ? 'Requires full sun, at least 6-8 hours of direct sunlight daily.'
                  : 'Provide adequate sunlight based on plant requirements.'} */}
              </Text>
            </View>
          </View>

          <View style={styles.careGuideItem}>
            <View style={[styles.careGuideIcon, { backgroundColor: colors.danger + '20' }]}>
              <Bug size={20} color={colors.danger} />
            </View>
            <View style={styles.careGuideContent}>
              <Text style={styles.careGuideTitle}>Pest Control</Text>
              <Text style={styles.careGuideText}>
                {plant.helpGuide?.pestControl || 'Monitor for common pests and treat promptly if detected.'}
                {/* {plant.species === 'Tomato'
                  ? 'Watch for aphids, hornworms, and whiteflies. Inspect leaves regularly.'
                  : 'Monitor for common pests and treat promptly if detected.'} */}
              </Text>
            </View>
          </View>

          <View style={styles.careGuideItem}>
            <View style={[styles.careGuideIcon, { backgroundColor: colors.success + '20' }]}>
              <Scissors size={20} color={colors.success} />
            </View>
            <View style={styles.careGuideContent}>
              <Text style={styles.careGuideTitle}>Pruning</Text>
              <Text style={styles.careGuideText}>
                {plant.helpGuide?.pruning || 'Prune dead or damaged growth as needed to maintain plant health.'}
                {/* {plant.species === 'Tomato'
                  ? 'Remove suckers and lower leaves to improve air circulation and prevent disease.'
                  : 'Prune dead or damaged growth as needed to maintain plant health.'} */}
              </Text>
            </View>
          </View>
        </View>

        {plantTasks.length > 0 && (
          <View style={styles.recentTasksContainer}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recent Tasks</Text>
              <TouchableOpacity onPress={() => setActiveTab('tasks')}>
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>

            {plantTasks.slice(0, 2).map(task => (
              <TouchableOpacity
                key={task.id}
                style={styles.taskItem}
                onPress={() => router.push(`/task/${task.id}`)}
              >
                <View style={[
                  styles.taskStatusIndicator,
                  { backgroundColor: getTaskStatusColor(task.status) }
                ]} />
                <View style={styles.taskContent}>
                  <Text style={styles.taskTitle}>{task.title}</Text>
                  <View style={styles.taskMeta}>
                    <View style={styles.taskMetaItem}>
                      <Calendar size={14} color={colors.gray[500]} />
                      <Text style={styles.taskMetaText}>
                        {new Date(task.dueDate).toLocaleDateString()}
                      </Text>
                    </View>
                    <View style={styles.taskMetaItem}>
                      <Clock size={14} color={colors.gray[500]} />
                      <Text style={styles.taskMetaText}>
                        {task.status === 'completed' ? 'Completed' : 'Due'}
                      </Text>
                    </View>
                  </View>
                </View>
                <ChevronRight size={20} color={colors.gray[400]} />
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    );
  };
  const renderTasksTab = () => {
    // const isInactive = plant.isInactive === true;

    return (
      <View style={styles.tabContent}>
        <View style={[styles.tabHeader, isRTL && { flexDirection: 'row-reverse' }]}>
          <Text style={styles.tabTitle}>{t('entity.plant.tasksTabTitle')}</Text>
          <TouchableOpacity
            style={[
              styles.addButton,
              isInactive && styles.disabledButton
            ]}
            onPress={handleCreateTask}
            disabled={isInactive}
          >
            <Plus size={20} color={colors.white} />
            <Text style={styles.addButtonText}>{t('entity.plant.createTask')}</Text>
          </TouchableOpacity>
        </View>

        {plantTasks.length > 0 ? (
          plantTasks.map(task => (
            <TouchableOpacity
              key={task.id}
              style={styles.taskItem}
              onPress={() => router.push(`/task/${task.id}`)}
            >
              <View style={[
                styles.taskStatusIndicator,
                { backgroundColor: getTaskStatusColor(task.status) }
              ]} />
              <View style={styles.taskContent}>
                <Text style={styles.taskTitle}>{task.title}</Text>
                {task.description && (
                  <Text style={styles.taskDescription} numberOfLines={2}>
                    {task.description}
                  </Text>
                )}
                <View style={styles.taskMeta}>
                  <View style={styles.taskMetaItem}>
                    <Calendar size={14} color={colors.gray[500]} />
                    <Text style={styles.taskMetaText}>
                      {new Date(task.dueDate).toLocaleDateString()}
                    </Text>
                  </View>
                  <View style={styles.taskMetaItem}>
                    <Clock size={14} color={colors.gray[500]} />
                    <Text style={styles.taskMetaText}>
                      {task.status === 'completed'
                        /**
                         * Render the checklists tab.
                         */
                        ? `Completed ${task.completedAt ? new Date(task.completedAt).toLocaleDateString() : ''}`
                        : 'Pending'}
                    </Text>
                  </View>
                </View>
              </View>
              <ChevronRight size={20} color={colors.gray[400]} />
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyContainer}>
            <ClipboardList size={40} color={colors.gray[400]} />
            <Text style={styles.emptyText}>{t('entity.plant.noTasks')}</Text>
            <TouchableOpacity
              style={styles.emptyButton}
              onPress={handleCreateTask}
              disabled={isInactive}
            >
              <Text style={styles.emptyButtonText}>{t('entity.plant.createTask')}</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };
  const renderHealthTab = () => (
    <View style={styles.tabContent}>
      {!healthAnalysis ? (
        <View style={styles.healthAnalysisContainer}>
          <View style={styles.noAnalysisContainer}>
            <AlertCircle size={40} color={colors.gray[400]} />
            <Text style={styles.noAnalysisText}>
              {t('entity.plant.noAnalysis')}
            </Text>
            <Text style={styles.noAnalysisSubtext}>
              {t('entity.plant.noAnalysisHint')}
            </Text>
            <Button
              title={isAnalyzing ? t('entity.plant.analyzing') : t('entity.plant.analyze')}
              // onPress={handleAnalyzePlantHealth}
              onPress={() => { setShowImagePickerModalAI(true) }}
              disabled={isAnalyzing || isInactive}
              style={styles.analyzeButton}
              leftIcon={
                isAnalyzing ? <ActivityIndicator size="small" color={colors.white} /> : undefined
              }
            />
          </View>
        </View>
      ) : (
        <View style={styles.healthAnalysisContainer}>
          <View style={styles.healthStatusContainer}>
            <View
              style={[
                styles.healthStatusBadge,
                {
                  backgroundColor:
                    healthAnalysis.healthStatus === 'excellent' ? colors.success + '20' :
                      healthAnalysis.healthStatus === 'good' ? colors.primary + '20' :
                        healthAnalysis.healthStatus === 'fair' ? colors.warning + '20' :
                          colors.danger + '20',
                },
              ]}
            >
              <Heart
                size={20}
                color={
                  healthAnalysis.healthStatus === 'excellent' ? colors.success :
                    healthAnalysis.healthStatus === 'good' ? colors.primary :
                      healthAnalysis.healthStatus === 'fair' ? colors.warning :
                        colors.danger
                }
              />
              <Text
                style={[
                  styles.healthStatusText,
                  {
                    color:
                      healthAnalysis.healthStatus === 'excellent' ? colors.success :
                        healthAnalysis.healthStatus === 'good' ? colors.primary :
                          healthAnalysis.healthStatus === 'fair' ? colors.warning :
                            colors.danger,
                    textAlign: isRTL ? 'right' : 'left',
                  },
                ]}
              >
                {t(`entity.plant.status_${healthAnalysis.healthStatus}`)}
              </Text>
            </View>
          </View>

          {healthAnalysis.conditions?.length > 0 && (
            <View style={styles.healthSection}>
              <Text style={[styles.healthSectionTitle, { textAlign: isRTL ? 'right' : 'left' }]}>
                {t('entity.plant.conditionsTitle')}
              </Text>
              {healthAnalysis.conditions.map((condition: string, index: number) => (
                <View key={index} style={styles.conditionItem}>
                  <AlertCircle size={16} color={colors.danger} style={styles.conditionIcon} />
                  <Text style={[styles.conditionText, { textAlign: isRTL ? 'right' : 'left' }]}>
                    {t(`entity.plant.${condition}`, condition)}
                  </Text>
                </View>
              ))}
            </View>
          )}

          {healthAnalysis.recommendations?.length > 0 && (
            <View style={styles.healthSection}>
              <Text style={[styles.healthSectionTitle, { textAlign: isRTL ? 'right' : 'left' }]}>
                {t('entity.plant.recommendationsTitle')}
              </Text>
              {healthAnalysis.recommendations.map((rec: string, index: number) => (
                <View key={index} style={styles.recommendationItem}>
                  <Check size={16} color={colors.primary} style={styles.recommendationIcon} />
                  <Text style={[styles.recommendationText, { textAlign: isRTL ? 'right' : 'left' }]}>
                    {t(`entity.plant.${rec}`, rec)}
                  </Text>
                </View>
              ))}
            </View>
          )}

          {healthAnalysis.details && (
            <View style={styles.healthSection}>
              <Text style={[styles.healthSectionTitle, { textAlign: isRTL ? 'right' : 'left' }]}>
                {t('entity.plant.detailsTitle')}
              </Text>
              <Text style={[styles.healthDetailsText, { textAlign: isRTL ? 'right' : 'left' }]}>
                {healthAnalysis.details}
              </Text>
            </View>
          )}

          <View style={styles.healthActions}>
            {/* <Button
              title={t('entity.plant.scheduleCheckup')}
              disabled={isInactive}
              onPress={() => {
                router.push({
                  pathname: '/task/create',
                  params: {
                    title: t('entity.plant.checkupTitle', { name: plant.name || 'Plant' }),
                    category: 'health',
                    priority: 'medium',
                  },
                });
              }}
              style={styles.healthActionButton}
              leftIcon={<Thermometer size={20} color={colors.white} />}
            /> */}

            {/* <Button
              title={t('entity.plant.recordTreatment')}
              disabled={isInactive}
              onPress={() => {
                // RNAlert.alert(t('common.featureComingSoon'), t('entity.plant.treatmentSoon'));
              }}
              style={[styles.healthActionButton, { backgroundColor: colors.secondary }]}
              leftIcon={<Pill size={20} color={colors.white} />}
            /> */}
          </View>
        </View>
      )}
    </View>
  );

  if (!plant || isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  // 
  // Fetch available checklists for plants
  const fetchAvailableChecklists = async () => {
    setIsLoadingChecklists(true);
    try {
      // Query checklists where entityType is 'plant' or the plant category ID
      const checklistsRef = collection(firestore, 'checklists');
      const q = query(
        checklistsRef,
        where('category', '==', 'plant') // Plant category ID//EIkF12Ewx9idmbF69wCJ
      );

      const querySnapshot = await getDocs(q);
      const checklists = [];

      querySnapshot.forEach((doc) => {
        checklists.push({
          id: doc.id,
          ...doc.data()
        });
      });

      setAvailableChecklists(checklists);
    } catch (error) {
      console.error('Error fetching checklists:', error);
      Toast.show({
        type: 'error',
        text1: t('common.error'),
        text2: t('entity.plant.checklist.fetchError')
      });
    } finally {
      setIsLoadingChecklists(false);
    }
  };

  // Handle checklist selection
  const handleSelectChecklist = (checklist: any) => {
    setSelectedChecklistTemplate(checklist);

    // console.log({ checklist })
    setShowChecklistSelector(false);
    setShowChecklistModal(true);
  };

  // Render checklist selector modal
  const renderChecklistSelector = () => (
    <Modal
      visible={showChecklistSelector}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowChecklistSelector(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{t('entity.plant.checklist.selectTitle')}</Text>
            <TouchableOpacity onPress={() => setShowChecklistSelector(false)}>
              <X size={24} color={colors.gray[700]} />
            </TouchableOpacity>
          </View>

          {isLoadingChecklists ? (
            <ActivityIndicator size="large" color={colors.primary} style={{ marginVertical: 20 }} />
          ) : availableChecklists.length > 0 ? (
            <FlatList
              data={availableChecklists}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.checklistOption}
                  onPress={() => handleSelectChecklist(item)}
                >
                  <View style={styles.checklistOptionContent}>
                    <ClipboardList size={20} color={colors.primary} />
                    <View style={styles.checklistOptionTextContainer}>
                      <Text style={styles.checklistOptionTitle}>{item.title}</Text>
                      {item.description && (
                        <Text style={styles.checklistOptionDescription}>{item.description}</Text>
                      )}
                    </View>
                  </View>
                  <ChevronRight size={20} color={colors.gray[400]} />
                </TouchableOpacity>
              )}
              contentContainerStyle={{ paddingBottom: 20 }}
            />
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>
                {t('entity.plant.checklist.noChecklists')}
              </Text>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );

  // When the "New Checklist" button is clicked, show the checklist selector
  const handleNewChecklist = () => {
    fetchAvailableChecklists();
    setShowChecklistSelector(true);
  };

  // Modified handleSaveChecklist to save to plantsChecklist collection
  // const handleSaveChecklist = async (items, images, notes) => {
  //   try {
  //     setIsLoadingAdd(true);

  //     // Create the checklist entry
  //     const newChecklistEntry = {
  //       id: Date.now().toString(),
  //       type: 'checklist_completed',
  //       date: new Date().toISOString(),
  //       userId: user?.id || '',
  //       userName: user?.name || '',
  //       plantId: plant.id,
  //       title: selectedChecklistTemplate?.title || 'Plant Inspection',
  //       description: notes || 'Completed plant health check',
  //       checklistItems: items,
  //       images,
  //       checklistTemplateId: selectedChecklistTemplate?.id || null,
  //       farmId: currentFarm?.id,
  //     };

  //     // Save to plantsChecklist collection
  //     const plantsChecklistRef = collection(firestore, 'plantsChecklist');
  //     await addDoc(plantsChecklistRef, {
  //       ...newChecklistEntry,
  //       createdAt: serverTimestamp(),
  //     });

  //     // Refresh the checklist data
  //     await loadTodayCheckListData();

  //     setShowChecklistModal(false);
  //     Alert.alert('Success', 'Checklist saved successfully');

  //   } catch (error) {
  //     console.error('Error saving checklist:', error);
  //     Alert.alert('Error', 'Failed to save checklist');
  //   } finally {
  //     setIsLoadingAdd(false);
  //   }
  // };

  // Render a single checklist item in the list
  const renderChecklistItem = ({ item }) => {
    const date = new Date(item.date);
    const isToday = date.toDateString() === new Date().toDateString();

    return (
      <View style={styles.checklistListItem}>
        <View style={styles.checklistListItemHeader}>
          <View style={styles.checklistListItemMeta}>
            {isToday && (
              <View style={styles.todayBadge}>
                <Text style={styles.todayBadgeText}>{t('entity.plant.checklist.todayBadge')}</Text>
              </View>
            )}
            <Text style={styles.checklistListItemDate}>
              {date.toLocaleDateString()}
            </Text>
          </View>
          <Text style={styles.checklistListItemTitle}>
            {item.title || t('entity.plant.checklist.defaultTitle')}
          </Text>
        </View>

        <View style={styles.checklistListItemSummary}>
          {/* {item.checklistItems && (
              <Text style={styles.checklistListItemStats}>
                {item.checklistItems.filter(i => i.completed).length} / {item.checklistItems.length} {t('entity.plant.checklist.itemsCompleted')}
              </Text>
            )} */}
          {item.userName && (
            <View style={styles.checklistListItemUser}>
              <User size={14} color={colors.gray[500]} />
              <Text style={styles.checklistListItemUserText}>{item.userName}</Text>
            </View>
          )}
        </View>

        <TouchableOpacity
          style={styles.viewDetailsButton}
          onPress={() => {
            // Show details of this checklist
            Alert.alert(
              item.title || t('entity.plant.checklist.title'),
              item.description || '',
              [
                {
                  text: t('common.view'),
                  onPress: () => {
                    // Show a modal with all checklist items
                    const itemsText = item.checklistItems
                      .map(item => `${item.title}: ${item.completed ? '✓' : '✗'}`)
                      .join('\n');
                    Alert.alert(t('entity.plant.checklist.items'), itemsText);
                  }
                },
                { text: t('common.close'), style: 'cancel' }
              ]
            );
          }}
        >
          <Text style={styles.viewDetailsButtonText}>
            {t('entity.plant.checklist.viewDetails')}
          </Text>
          <ChevronRight size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>
    );
  };


  const renderChecklistsTab = () => {
    const isInactive = plant?.isInactive === true;


    return (
      <View style={styles.tabContent}>
        <EntityChecklistManager
          entityType="plant"
          entityId={plant?.id}
          farmId={currentFarm?.id}
          onSelectChecklist={handleSelectChecklist}
          disabled={isInactive}
        />
      </View>
    );
  };
  const renderHistoryTab = () => {
    return (
      <View style={styles.tabContent}>
        <Text style={styles.tabTitle}>{t('entity.plant.history')}</Text>
        {checkListSelectedData.length > 0 ? (
          checkListSelectedData.map((entry, index) => (
            // console.log('renderHistoryTab: entry:', entry),
            <View key={entry.id} style={styles.historyItem}>
              <View style={styles.historyItemHeader}>
                <View style={styles.historyItemType}>
                  {entry.type === 'checklist_completed' && (
                    <CheckCircle size={16} color={colors.success} />
                  )}
                  {entry.type === 'status_change' && (
                    <Sprout size={16} color={colors.primary} />
                  )}
                  {entry.type === 'note_added' && (
                    <Leaf size={16} color={colors.info} />
                  )}
                  <Text style={styles.historyItemTypeText}>
                    {t(`entity.plant.histories.types.${entry.type}`)}
                  </Text>
                </View>
                <Text style={styles.historyItemDate}>
                  {new Date(entry.date).toLocaleDateString()}
                </Text>
              </View>

              <Text style={styles.historyItemTitle}>{entry.title}</Text>
              <Text style={styles.historyItemDescription}>{entry.description}</Text>

              {entry.images?.length > 0 && (
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.historyImagesContainer}
                >
                  {entry.images.map((image, imgIndex) => (
                    // console.log('renderHistoryTab: image:', image),
                    <Image
                      key={imgIndex}
                      source={{ uri: image }}
                      style={styles.historyImage}
                    />
                  ))}
                </ScrollView>
              )}

              <View style={styles.historyItemFooter}>
                <Text style={styles.historyItemUser}>
                  {t('plant.history.by', { name: entry.userName })}
                </Text>
              </View>

              {index < history.length - 1 && <View style={styles.historyItemDivider} />}
            </View>
          ))
        ) : (
          <View style={styles.emptyContainer}>
            <History size={40} color={colors.gray[400]} />
            <Text style={styles.emptyText}>{t('entity.plant.histories.empty')}</Text>
          </View>
        )}
      </View>
    );
  };


  const getStatusColor = (status: string) => {
    if (status === 'inactive') return colors.gray[500];

    switch (status) {
      case 'seedling':
        return colors.info;
      case 'growing':
        return colors.success;
      case 'flowering':
        return colors.primary;
      case 'fruiting':
        return colors.warning;
      case 'harvested':
        return colors.gray[500];
      case 'dormant':
        return colors.gray[400];
      default:
        return colors.gray[500];
    }
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent':
        return colors.success;
      case 'good':
        return colors.primary;
      case 'fair':
        return colors.warning;
      case 'poor':
        return colors.danger;
      default:
        return colors.gray[500];
    }
  };

  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return colors.success;
      case 'in_progress':
        return colors.info;
      case 'pending':
        return colors.warning;
      case 'cancelled':
        return colors.gray[500];
      default:
        return colors.gray[500];
    }
  };

  const formatStatus = (status: string) => {
    if (status === 'inactive') return 'Inactive';

    return t(`entity.plant.${status}`)
    // return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const formatHealth = (health: string) => {
    // return health.charAt(0).toUpperCase() + health.slice(1);
    return t(`entity.plant.status_${health}`)
  };

  const formatHistoryType = (type: string) => {
    switch (type) {
      case 'checklist_completed':
        return 'Checklist';
      case 'status_change':
        return 'Status Update';
      case 'note_added':
        return 'Note';
      case 'image_added':
        return 'Photo';
      case 'task_completed':
        return 'Task';
      default:
        return type.replace('_', ' ');
    }
  };

  // Replace the renderGalleryTab function with this:
  const renderGalleryTab = () => {
    const handlePhotosUpdate = async (updatedPhotos: any[]) => {
      try {
        // Update local state - the actual gallery collection is managed by EntityGallery
        setPlant(prev => ({
          ...prev,
          photos: updatedPhotos,
          updatedAt: new Date()
        }));
      } catch (error) {
        console.error('Error updating plant photos:', error);
        throw error;
      }
    };

    return (
      <View style={styles.tabContent}>
        <EntityGallery
          entityId={plant.id}
          entityType="plant"
          photos={plant.photos || []}
          onPhotosUpdate={handlePhotosUpdate}
          isEditable={!plant.isInactive}
          maxPhotos={15}
          showTimestamp={true}
          showDeleteOption={true}
          gridColumns={3}
          imageSize="medium"
          emptyStateMessage={t('entity.plant.galleries.emptyMessage')}
        />
      </View>
    );
  };
  // const renderGalleryTab = () => {
  //   const handlePhotosUpdate = async (updatedPhotos: any[]) => {
  //     try {
  //       // Update the plant with new photos
  //       const updatedPlant = {
  //         ...plant,
  //         photos: updatedPhotos,
  //         updatedAt: new Date()
  //       };

  //       await updatePlant(plant.id, updatedPlant);
  //       setPlant(updatedPlant);
  //     } catch (error) {
  //       console.error('Error updating plant photos:', error);
  //       throw error;
  //     }
  //   };

  //   return (
  //     <View style={styles.tabContent}>
  //       <View style={styles.tabHeader}>
  //         <Text style={styles.tabTitle}>{t('entity.plant.galleries.title')}</Text>
  //         <TouchableOpacity
  //           style={[
  //             styles.addButton,
  //             isInactive && styles.disabledButton
  //           ]}
  //           onPress={() => setShowImagePickerModal(true)}
  //           disabled={isInactive}
  //         >
  //           <Plus size={20} color={colors.white} />
  //           <Text style={styles.addButtonText}>{t('entity.plant.galleries.addPhoto')}</Text>
  //         </TouchableOpacity>
  //       </View>

  //       {gallery.length > 0 ? (
  //         <View style={styles.galleryGrid}>
  //           {gallery.map((image, index) => (
  //             <TouchableOpacity
  //               key={index}
  //               style={styles.galleryImageContainer}
  //               onPress={() => {
  //                 // In a real app, you would show a full-screen image viewer
  //                 Alert.alert('Image', 'View full image');
  //               }}
  //             >
  //               <Image
  //                 source={{ uri: image }}
  //                 style={styles.galleryImage}
  //               />
  //             </TouchableOpacity>
  //           ))}
  //         </View>
  //       ) : (
  //         <View style={styles.emptyContainer}>
  //           <Camera size={40} color={colors.gray[400]} />
  //           <Text style={styles.emptyText}>{t('plant.photos.emptyText')}</Text>
  //           <TouchableOpacity
  //             style={[
  //               styles.emptyButton,
  //               isInactive && styles.disabledButton
  //             ]}
  //             onPress={() => setShowImagePickerModal(true)}
  //             disabled={isInactive}
  //           >
  //             <Text style={styles.emptyButtonText}>{t('plant.photos.addButton')}</Text>
  //           </TouchableOpacity>
  //         </View>
  //       )}
  //     </View>
  //   );
  // };

  const renderQRCodeTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.tabHeader}>
        <Text style={styles.tabTitle}>{t('entity.plant.qrCodeTabTitle')}</Text>
      </View>

      <View style={styles.qrCodeContainer}>
        <QRCodeDisplay
          value={plant.qrCodeLink || generateUniversalLink('plant', plant.itemId || plant.id, currentFarm.id)}
          itemId={plant.itemId || plant.id}
          itemType="plant"
          size={200}
        />

        <View style={styles.qrCodeInfo}>
          <Text style={styles.qrCodeInfoText}>
            {t('entity.plant.qrCodeInfo.scan')}
          </Text>
          <Text style={styles.qrCodeInfoText}>
            {t('entity.plant.qrCodeInfo.print')}
          </Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: plant.name,

        }}
      />
      <View style={styles.imageContainer}>
        <Image
          source={{
            uri: plant.image ||
              'https://images.unsplash.com/photo-1591857177580-dc82b9ac4e1e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80'
          }}
          style={styles.plantImage}
        />

        <View style={styles.imageOverlay}>
          <View style={styles.plantStatus}>
            <View style={[
              styles.statusBadge,
              { backgroundColor: plant.isInactive ? colors.gray[500] : getStatusColor(plant.status) }
            ]}>
              <Text style={styles.statusBadgeText}>
                {plant.isInactive ? 'Inactive' : formatStatus(plant.status)}
              </Text>
            </View>

            <View style={[
              styles.statusBadge,
              { backgroundColor: getHealthColor(plant.health) }
            ]}>
              <Text style={styles.statusBadgeText}>{formatHealth(plant.health)}</Text>
            </View>
          </View>
        </View>
      </View>

      {plant.isInactive === true && (
        <View style={[
          styles.inactiveBanner,
          { flexDirection: isRTL ? 'row-reverse' : 'row' }
        ]}>
          <Archive size={16} color={colors.white} />
          <Text
            style={[
              styles.inactiveBannerText,
              { textAlign: isRTL ? 'right' : 'left' },
              isRTL && { marginRight: 8 }
            ]}
          >
            {t('entity.plant.inactiveMessage')}
            {plant.inactiveReason ? `: ${plant.inactiveReason}` : ''}
            {plant.inactiveDate ? ` (${new Date(plant.inactiveDate).toLocaleDateString()})` : ''}
          </Text>
        </View>
      )}

      <View style={styles.tabsContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={[
            styles.tabs,
            { flexDirection: isRTL ? 'row-reverse' : 'row' }
          ]}
        >
          <TouchableOpacity
            style={[styles.tab, activeTab === 'overview' && styles.activeTab]}
            onPress={() => setActiveTab('overview')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'overview' && styles.activeTabText,
                { textAlign: isRTL ? 'right' : 'left' }
              ]}
            >
              {t('entity.plant.overview')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'health' && styles.activeTab]}
            onPress={() => setActiveTab('health')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'health' && styles.activeTabText,
                { textAlign: isRTL ? 'right' : 'left' }
              ]}
            >
              {t('entity.plant.health')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'tasks' && styles.activeTab]}
            onPress={() => setActiveTab('tasks')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'tasks' && styles.activeTabText,
                { textAlign: isRTL ? 'right' : 'left' }
              ]}
            >
              {t('entity.plant.tasks')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'checklists' && styles.activeTab]}
            onPress={() => setActiveTab('checklists')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'checklists' && styles.activeTabText,
                { textAlign: isRTL ? 'right' : 'left' }
              ]}
            >
              {t('entity.plant.checklists')}
            </Text>
          </TouchableOpacity>

          {/* <TouchableOpacity
            style={[styles.tab, activeTab === 'history' && styles.activeTab]}
            onPress={() => setActiveTab('history')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'history' && styles.activeTabText,
                { textAlign: isRTL ? 'right' : 'left' }
              ]}
            >
              {t('entity.plant.history')}
            </Text>
          </TouchableOpacity> */}

          <TouchableOpacity
            style={[styles.tab, activeTab === 'gallery' && styles.activeTab]}
            onPress={() => setActiveTab('gallery')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'gallery' && styles.activeTabText,
                { textAlign: isRTL ? 'right' : 'left' }
              ]}
            >
              {t('entity.plant.gallery')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'qrcode' && styles.activeTab]}
            onPress={() => setActiveTab('qrcode')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'qrcode' && styles.activeTabText,
                { textAlign: isRTL ? 'right' : 'left' }
              ]}
            >
              {t('entity.plant.qrcode')}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
      <ScrollView style={styles.content}>
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'tasks' && renderTasksTab()}
        {activeTab === 'health' && renderHealthTab()}
        {activeTab === 'checklists' && renderChecklistsTab()}
        {/* {activeTab === 'history' && renderHistoryTab()} */}
        {activeTab === 'gallery' && renderGalleryTab()}
        {activeTab === 'qrcode' && renderQRCodeTab()}
      </ScrollView>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={showDeleteConfirm}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDeleteConfirm(false)}
      >
        <View style={styles.confirmModalOverlay}>
          <View style={styles.confirmModalContent}>
            <Text style={styles.confirmModalTitle}>Delete Plant</Text>
            <Text style={styles.confirmModalText}>
              Are you sure you want to delete this plant? This action cannot be undone.
            </Text>

            <View style={styles.confirmModalButtons}>
              <TouchableOpacity
                style={[styles.confirmModalButton, styles.confirmModalCancelButton]}
                onPress={() => setShowDeleteConfirm(false)}
              >
                <Text style={styles.confirmModalCancelText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.confirmModalButton, styles.confirmModalDeleteButton]}
                onPress={handleDeletePlant}
              >
                <Text style={styles.confirmModalDeleteText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Checklist Modal */}
      <Modal
        visible={showChecklistModal}
        animationType="slide"
        onRequestClose={() => setShowChecklistModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <ChecklistForm
            title={`${plant.name} Health Check`}
            checklist={selectedChecklistTemplate}
            onSave={handleSaveChecklist}
            onCancel={() => setShowChecklistModal(false)}
            requireImages={true}
            isLoadingAdd={isLoadingAdd}
          />
        </SafeAreaView>
      </Modal>

      {/* Image Picker Modal */}
      <Modal
        visible={showImagePickerModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowImagePickerModal(false)}
      >
        <View style={styles.imagePickerModalOverlay}>
          <View style={styles.imagePickerModalContent}>
            <View style={styles.imagePickerModalHeader}>
              <Text style={styles.imagePickerModalTitle}>Add Photo</Text>
              <TouchableOpacity onPress={() => setShowImagePickerModal(false)}>
                <X size={24} color={colors.gray[700]} />
              </TouchableOpacity>
            </View>

            <View style={styles.imagePickerContainer}>
              <ImagePicker
                image=""
                onImageSelected={handleAddImage}
                placeholder="Tap to select an image"
                size={200}
              />
            </View>

            <Text style={styles.imagePickerText}>
              Take a photo or select one from your gallery
            </Text>
          </View>
        </View>
      </Modal>

      <Modal
        visible={showImagePickerModalAI}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowImagePickerModalAI(false)}
      >
        <View style={styles.imagePickerModalOverlay}>
          <View style={styles.imagePickerModalContent}>
            <View style={styles.imagePickerModalHeader}>
              <Text style={styles.imagePickerModalTitle}>Add Photo</Text>
              <TouchableOpacity onPress={() => setShowImagePickerModalAI(false)}>
                <X size={24} color={colors.gray[700]} />
              </TouchableOpacity>
            </View>

            <View style={styles.imagePickerContainer}>
              <ImagePicker
                image=""
                onImageSelected={(uri) => handleAnalyzePlantHealth(uri)}
                placeholder="Tap to select an image"
                size={200}
              />
            </View>

            <Text style={styles.imagePickerText}>
              Take a photo or select one from your gallery
            </Text>
          </View>
        </View>
      </Modal>

      {/* Inactive Status Modal */}
      <InactiveStatusModal
        visible={showInactiveModal}
        onClose={() => setShowInactiveModal(false)}
        onSubmit={handleMarkInactive}
        entityType="plant"
        showCascadeOption={false}
      />

      {/* Finance Bottom Sheet */}
      {/* <FinanceBottomSheet
        isVisible={showFinanceBottomSheet}
        onClose={handleCloseFinanceBottomSheet}
        entityId={plant.id}
        entityType="plant"
        bottomSheetRef={financeBottomSheetRef}
      /> */}
    </SafeAreaView>
  );
}

// Helper functions


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  checklistActions: {
    marginTop: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveChecklistButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
    marginTop: 16,
  },
  healthAnalysisContainer: {
    marginBottom: 20,
  },
  noAnalysisContainer: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
  },
  noAnalysisText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginTop: 12,
    marginBottom: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 12,
    width: '90%',
    maxHeight: '80%',
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  checklistOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  checklistOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  checklistOptionTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  checklistOptionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  checklistOptionDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginTop: 4,
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
  },
  checklistsContainer: {
    marginTop: 16,
  },
  previousChecklistItem: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  previousChecklistHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  previousChecklistDate: {
    fontSize: 14,
    color: colors.gray[600],
  },
  previousChecklistTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  viewDetailsButtonText: {
    fontSize: 14,
    color: colors.primary,
    marginRight: 4,
  },
  completeChecklistButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  completeChecklistButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '500',
  },
  noAnalysisSubtext: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: 20,
  },
  analyzeButton: {
    width: '100%',
    backgroundColor: colors.primary,
  },
  healthStatusContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  healthStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  healthStatusText: {
    fontSize: 14,
    color: colors.gray[700],
  },
  checklistListContainer: {
    paddingBottom: 20,
  },
  checklistListItem: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  checklistListItemHeader: {
    marginBottom: 12,
  },
  checklistListItemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  todayBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 8,
  },
  todayBadgeText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '500',
  },
  checklistListItemDate: {
    fontSize: 14,
    color: colors.gray[600],
  },
  checklistListItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  checklistListItemSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  checklistListItemStats: {
    fontSize: 14,
    color: colors.gray[700],
  },
  checklistListItemUser: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checklistListItemUserText: {
    fontSize: 14,
    color: colors.gray[700],
    marginLeft: 4,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  completeChecklistButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  completeChecklistButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '500',
  },
  healthStatusText: {
    fontSize: 14,
    color: colors.gray[700],
  },
  checklistListContainer: {
    paddingBottom: 20,
  },
  checklistListItem: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  checklistListItemHeader: {
    marginBottom: 12,
  },
  checklistListItemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  todayBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 8,
  },
  todayBadgeText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '500',
  },
  checklistListItemDate: {
    fontSize: 14,
    color: colors.gray[600],
  },
  checklistListItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  checklistListItemSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  checklistListItemStats: {
    fontSize: 14,
    color: colors.gray[700],
  },
  checklistListItemUser: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checklistListItemUserText: {
    fontSize: 14,
    color: colors.gray[700],
    marginLeft: 4,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  completeChecklistButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  completeChecklistButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '500',
  },
  healthStatusText: {
    fontSize: 14,
    color: colors.gray[700],
  },
  checklistListContainer: {
    paddingBottom: 20,
  },
  checklistListItem: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  checklistListItemHeader: {
    marginBottom: 12,
  },
  checklistListItemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  todayBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 8,
  },
  todayBadgeText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '500',
  },
  checklistListItemDate: {
    fontSize: 14,
    color: colors.gray[600],
  },
  checklistListItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  checklistListItemSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  checklistListItemStats: {
    fontSize: 14,
    color: colors.gray[700],
  },
  checklistListItemUser: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checklistListItemUserText: {
    fontSize: 14,
    color: colors.gray[700],
    marginLeft: 4,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  completeChecklistButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  completeChecklistButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '500',
  },
  healthStatusText: {
    fontSize: 14,
    color: colors.gray[700],
  },
  checklistListContainer: {
    paddingBottom: 20,
  },
  checklistListItem: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  checklistListItemHeader: {
    marginBottom: 12,
  },
  checklistListItemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  todayBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 8,
  },
  todayBadgeText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '500',
  },
  checklistListItemDate: {
    fontSize: 14,
    color: colors.gray[600],
  },
  checklistListItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  checklistListItemSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  checklistListItemStats: {
    fontSize: 14,
    color: colors.gray[700],
  },
  checklistListItemUser: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checklistListItemUserText: {
    fontSize: 14,
    color: colors.gray[700],
    marginLeft: 4,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  completeChecklistButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  completeChecklistButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '500',
  },
  healthStatusText: {
    fontSize: 14,
    color: colors.gray[700],
  },
  checklistListContainer: {
    paddingBottom: 20,
  },
  checklistListItem: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  checklistListItemHeader: {
    marginBottom: 12,
  },
  checklistListItemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  todayBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 8,
  },
  todayBadgeText: {
    color: colors.white,
    fontSize: 12,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  healthSection: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  healthSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  conditionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  conditionIcon: {
    marginRight: 8,
  },
  conditionText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  recommendationIcon: {
    marginRight: 8,
  },
  recommendationText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  healthDetailsText: {
    fontSize: 14,
    lineHeight: 20,
    color: colors.gray[700],
  },
  healthActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  healthActionButton: {
    flex: 1,
    marginHorizontal: 4,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
  },
  optionsMenu: {
    position: 'absolute',
    top: 50,
    right: 16,
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 8,
    zIndex: 10,
    shadowColor: colors.gray[800],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  optionText: {
    fontSize: 14,
    color: colors.gray[800],
    marginLeft: 8,
  },
  imageContainer: {
    height: 200,
    position: 'relative',
  },
  plantImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  plantStatus: {
    flexDirection: 'row',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  statusBadgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.white,
  },
  tabsContainer: {
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  tabs: {
    paddingHorizontal: 16,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.gray[600],
    fontWeight: '500',
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    padding: 16,
  },
  infoCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  infoItem: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    color: colors.gray[800],
    fontWeight: '500',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    color: colors.gray[800],
    fontWeight: '500',
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary + '10',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  locationText: {
    fontSize: 14,
    color: colors.primary,
    marginLeft: 4,
  },
  notesContainer: {
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  notesLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
  },
  notesText: {
    fontSize: 14,
    color: colors.gray[800],
    lineHeight: 20,
  },
  quickActionsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  quickActionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  careGuideContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  careGuideItem: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  careGuideIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  careGuideContent: {
    flex: 1,
  },
  careGuideTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  careGuideText: {
    fontSize: 14,
    color: colors.gray[600],
    lineHeight: 20,
  },
  recentTasksContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  seeAllText: {
    fontSize: 14,
    color: colors.primary,
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.gray[300],
  },
  taskStatusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 12,
  },
  taskContent: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
    marginBottom: 4,
  },
  taskDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  taskMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  taskMetaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  taskMetaText: {
    fontSize: 12,
    color: colors.gray[500],
    marginLeft: 4,
  },
  tabHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  tabTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
    marginLeft: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    backgroundColor: colors.white,
    borderRadius: 12,
    marginTop: 16,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[600],
    marginTop: 12,
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  emptyButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  checklistTypes: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  checklistTypeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: colors.gray[100],
  },
  activeChecklistTypeButton: {
    backgroundColor: colors.primary,
  },
  checklistTypeText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  activeChecklistTypeText: {
    color: colors.white,
    fontWeight: '500',
  },
  checklistContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  checklistTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 16,
  },
  checklistItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: colors.gray[400],
    marginRight: 12,
    marginTop: 2,
  },
  checkboxChecked: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checklistItemContent: {
    flex: 1,
  },
  checklistItemTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
    marginBottom: 2,
  },
  checklistItemDescription: {
    fontSize: 14,
    color: colors.gray[600],
  },
  checklistFooter: {
    marginTop: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  checklistFooterText: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
  },
  completeChecklistButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  completeChecklistButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  historyItem: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  historyItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  historyItemType: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  historyItemTypeText: {
    fontSize: 12,
    color: colors.gray[800],
    marginLeft: 4,
  },
  historyItemDate: {
    fontSize: 12,
    color: colors.gray[500],
  },
  historyItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  historyItemDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 12,
    lineHeight: 20,
  },
  historyImagesContainer: {
    marginBottom: 12,
  },
  historyImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginRight: 8,
  },
  historyItemFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  historyItemUser: {
    fontSize: 12,
    color: colors.gray[500],
  },
  historyItemDivider: {
    height: 1,
    backgroundColor: colors.gray[200],
    marginVertical: 16,
  },
  galleryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  galleryImageContainer: {
    width: '32%',
    aspectRatio: 1,
    marginBottom: 8,
    borderRadius: 8,
    overflow: 'hidden',
  },
  galleryImage: {
    width: '100%',
    height: '100%',
  },
  confirmModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmModalContent: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxWidth: 400,
  },
  confirmModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  confirmModalText: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 20,
    lineHeight: 20,
  },
  confirmModalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  confirmModalButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginLeft: 12,
  },
  confirmModalCancelButton: {
    backgroundColor: colors.gray[200],
  },
  confirmModalCancelText: {
    color: colors.gray[800],
    fontWeight: '500',
  },
  confirmModalDeleteButton: {
    backgroundColor: colors.danger,
  },
  confirmModalDeleteText: {
    color: colors.white,
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  imagePickerModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  imagePickerModalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  imagePickerModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  imagePickerModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  imagePickerContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  imagePickerText: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: 20,
  },
  // QR Code tab styles
  qrCodeContainer: {
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  qrCodeInfo: {
    marginTop: 20,
    padding: 16,
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    width: '100%',
  },
  qrCodeInfoText: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  // Item ID styles in overview tab
  itemIdContainer: {
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  itemIdHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  itemIdTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginLeft: 8,
  },
  itemIdValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 8,
  },
  viewQrButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  viewQrButtonText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  disabledActionButton: {
    opacity: 0.6,
  },
  disabledActionText: {
    color: colors.gray[500],
  },
  disabledButton: {
    backgroundColor: colors.gray[400],
    opacity: 0.6,
  },
  inactiveBanner: {
    backgroundColor: colors.gray[700],
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  inactiveBannerText: {
    color: colors.white,
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  inactiveDetailsContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.gray[500],
  },
  inactiveDetailsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  inactiveDetailsRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  inactiveDetailsLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    width: 80,
  },
  inactiveDetailsValue: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  inactiveImageContainer: {
    marginTop: 8,
  },
  inactiveImage: {
    width: '100%',
    height: 150,
    borderRadius: 8,
    marginTop: 8,
  },
});
